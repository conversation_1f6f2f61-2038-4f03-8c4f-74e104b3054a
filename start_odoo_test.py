#!/usr/bin/env python3
"""
Simple script to start Odoo and test if the fixes work.
"""

import sys
import os
import subprocess
import time

def start_odoo_server():
    """Start the Odoo server and check for errors."""
    print("🚀 Starting Odoo server to test ASGI migration fixes...")
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = '/home/<USER>/midlogic/odoo_erp'
    
    # Command to start Odoo
    cmd = [
        '/home/<USER>/midlogic/odoo_erp/venv/bin/python',
        '/home/<USER>/midlogic/odoo_erp/odoo-bin',
        '--config=/home/<USER>/midlogic/odoo_erp/odoo.conf',
        '--log-level=info',
        '--stop-after-init'  # Stop after initialization to test startup
    ]
    
    print(f"📝 Command: {' '.join(cmd)}")
    print("⏳ Starting server...")
    
    try:
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env,
            cwd='/home/<USER>/midlogic/odoo_erp'
        )
        
        # Wait for the process to complete (with timeout)
        stdout, stderr = process.communicate(timeout=60)
        
        print("📊 Server startup completed")
        print(f"Return code: {process.returncode}")
        
        # Check for the specific errors we fixed
        pgsql_errors = []
        id_errors = []
        
        full_output = stdout + stderr
        
        for line in full_output.split('\n'):
            if "name 'pgsql' is not defined" in line:
                pgsql_errors.append(line.strip())
            if "NameError: name 'id' is not defined" in line:
                id_errors.append(line.strip())
        
        # Report results
        print("\n📋 Error Analysis:")
        print(f"  🔍 pgsql errors found: {len(pgsql_errors)}")
        print(f"  🔍 id parameter errors found: {len(id_errors)}")
        
        if pgsql_errors:
            print("\n❌ PGSQL errors still exist:")
            for error in pgsql_errors[:3]:  # Show first 3
                print(f"    {error}")
        else:
            print("\n✅ No pgsql errors found!")
        
        if id_errors:
            print("\n❌ ID parameter errors still exist:")
            for error in id_errors[:3]:  # Show first 3
                print(f"    {error}")
        else:
            print("\n✅ No id parameter errors found!")
        
        # Show last few lines of output for context
        print("\n📝 Last few lines of output:")
        lines = full_output.strip().split('\n')
        for line in lines[-10:]:
            if line.strip():
                print(f"    {line}")
        
        # Determine success
        if len(pgsql_errors) == 0 and len(id_errors) == 0:
            print("\n🎉 SUCCESS: The ASGI migration fixes are working!")
            print("   No more pgsql or id parameter errors during startup.")
            return True
        else:
            print("\n❌ FAILURE: Some errors still exist.")
            return False
        
    except subprocess.TimeoutExpired:
        print("⏰ Server startup timed out (60 seconds)")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False

def main():
    """Main function."""
    print("🔧 Testing Odoo Server Startup After ASGI Migration Fixes")
    print("=" * 60)
    
    # Check if PostgreSQL is running
    try:
        result = subprocess.run(['docker', 'compose', 'ps', '-q', 'postgres'], 
                              capture_output=True, text=True, 
                              cwd='/home/<USER>/midlogic/odoo_erp')
        if not result.stdout.strip():
            print("🐳 Starting PostgreSQL...")
            subprocess.run(['docker', 'compose', 'up', '-d', 'postgres'], 
                         cwd='/home/<USER>/midlogic/odoo_erp')
            time.sleep(5)  # Wait for PostgreSQL to start
    except Exception as e:
        print(f"⚠️  Could not check/start PostgreSQL: {e}")
    
    # Test server startup
    success = start_odoo_server()
    
    if success:
        print("\n✅ ASGI migration fixes are working correctly!")
        print("   The server can start without the original errors.")
    else:
        print("\n❌ Some issues may still exist.")
        print("   Check the output above for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
